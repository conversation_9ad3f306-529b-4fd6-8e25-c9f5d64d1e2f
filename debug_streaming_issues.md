# 🐛 Debugging Guide: LiteLLM Streaming Issues with Gemini

## Problem Summary
The `litellm.exceptions.APIConnectionError` with JSON parsing errors occurs when LiteLLM receives malformed chunks from Gemini's streaming API, particularly with tool-heavy requests.

## 🛠️ Fixes Applied

### 1. **Fixed Pydantic Deprecation** ✅
- Changed `tool_obj.dict()` to `tool_obj.model_dump()` 
- This ensures proper tool serialization for Gemini API

### 2. **Enhanced Streaming Error Handling** ✅
- Added detection and skipping of malformed chunks (`{`, `}`, empty strings)
- Improved JSON parsing error recovery
- Graceful continuation when encountering parsing errors

### 3. **Safety Fallback Mechanism** ✅
- Automatically uses non-streaming mode for requests with >10 tools
- Falls back to non-streaming if streaming fails with JSON errors
- Prevents crashes while maintaining functionality

### 4. **Debug Mode Support** ✅
- Set `LITELLM_DEBUG=true` environment variable to enable detailed logging
- Provides raw API request/response inspection

## 🚀 Usage Instructions

### Normal Operation
```bash
# Start server normally
python server.py
```

### Debug Mode
```bash
# Enable debug mode for detailed logging
LITELLM_DEBUG=true python server.py
```

### Environment Variables
```bash
# Add to your .env file for persistent debug mode
LITELLM_DEBUG=true
GEMINI_API_KEY=your_api_key_here
```

## 🔍 What to Expect

### Tool-Heavy Requests (>10 tools)
- Server will automatically use non-streaming mode
- Log message: `🛡️ SAFETY: Using non-streaming mode for tool-heavy request`

### Streaming Errors
- Malformed chunks will be skipped with warning logs
- Server continues processing instead of crashing
- Automatic fallback to non-streaming if needed

### Debug Output
When `LITELLM_DEBUG=true`:
- Raw API requests to Gemini
- Detailed chunk-by-chunk streaming data
- Internal LiteLLM processing logs

## 🧪 Testing the Fixes

1. **Test with many tools** (should use non-streaming automatically)
2. **Test with streaming enabled** (should handle errors gracefully)
3. **Test with debug mode** (should show detailed logs)

## 📊 Monitoring

Watch for these log messages:
- `💰 COST OPTIMIZATION: Disabled thinking mode` - Cost saving active
- `🛡️ SAFETY: Using non-streaming mode` - Safety fallback triggered
- `🔄 Streaming failed with JSON error, falling back` - Error recovery
- `Skipping malformed/incomplete chunk` - Chunk filtering working

## 🔧 Additional Debugging

If issues persist:

1. **Enable full debug mode**:
   ```bash
   LITELLM_DEBUG=true python server.py
   ```

2. **Check LiteLLM version**:
   ```bash
   pip show litellm
   ```

3. **Test with fewer tools** to isolate the issue

4. **Monitor network connectivity** during streaming requests

## 🎯 Expected Behavior

- ✅ Requests with ≤10 tools: Use streaming mode with error recovery
- ✅ Requests with >10 tools: Automatically use non-streaming mode  
- ✅ JSON parsing errors: Skip malformed chunks, continue processing
- ✅ Severe streaming errors: Fall back to non-streaming mode
- ✅ All requests: Apply cost optimization for Gemini 2.5 Flash

The server should now be much more resilient to the streaming JSON parsing issues you encountered.
